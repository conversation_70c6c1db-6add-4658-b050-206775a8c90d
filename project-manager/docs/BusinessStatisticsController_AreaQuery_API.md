# BusinessStatisticsController 地区查询接口文档

## 接口概述

基于现有的 `/uportal/project/area-query` 接口，在 `BusinessStatisticsController` 类中新增了一个同样的接口，但该接口只返回地区信息，不包含商机信息。

## 接口详情

### 接口路径
```
POST /uportal/business-statistics/area-query
```

### 接口描述
查询区域树结构，返回纯地区信息，不包含商机/项目数据。

### 请求参数

#### 请求体 (JSON)
```json
{
    "areaName": "地区名称（可选）"
}
```

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| areaName | String | 否 | 地区名称，用于过滤特定地区。如果为空或不传，则返回所有地区 |

### 响应结果

#### 成功响应
```json
{
    "success": true,
    "code": "200",
    "message": "操作成功",
    "data": [
        {
            "id": "地区ID",
            "areaName": "地区名称",
            "areaLevel": 1,
            "parentId": "父级地区ID",
            "pathName": "地区路径名称",
            "addCheck": true,
            "isOppo": false,
            "children": [
                {
                    "id": "子地区ID",
                    "areaName": "子地区名称",
                    "areaLevel": 2,
                    "parentId": "父级地区ID",
                    "pathName": "子地区路径名称",
                    "addCheck": false,
                    "isOppo": false,
                    "children": []
                }
            ]
        }
    ]
}
```

#### 响应字段说明

| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | String | 地区唯一标识符 |
| areaName | String | 地区名称 |
| areaLevel | Integer | 地区层级（1-一级地区，2-二级地区，等） |
| parentId | String | 父级地区ID |
| pathName | String | 地区路径名称 |
| addCheck | Boolean | 是否可添加（二级地区为true） |
| isOppo | Boolean | 是否为商机/项目（此接口始终为false） |
| children | Array | 子地区列表 |

#### 错误响应
```json
{
    "success": false,
    "code": "500",
    "message": "Internal server error: 具体错误信息",
    "data": null
}
```

## 与原接口的区别

### 原接口 (`/uportal/project/area-query`)
- 返回地区树结构
- **包含商机/项目信息**（isOppo=true的节点）
- 受用户权限控制

### 新接口 (`/uportal/business-statistics/area-query`)
- 返回地区树结构
- **不包含商机/项目信息**（所有节点isOppo=false）
- 只返回纯地区数据
- 不受项目权限控制，只返回地区结构

## 使用示例

### 请求示例1：查询所有地区
```bash
curl -X POST \
  http://localhost:8080/uportal/business-statistics/area-query \
  -H 'Content-Type: application/json' \
  -d '{}'
```

### 请求示例2：查询特定地区
```bash
curl -X POST \
  http://localhost:8080/uportal/business-statistics/area-query \
  -H 'Content-Type: application/json' \
  -d '{
    "areaName": "北京"
  }'
```

### 响应示例
```json
{
    "success": true,
    "code": "200",
    "message": "操作成功",
    "data": [
        {
            "id": "1",
            "areaName": "中国",
            "areaLevel": 1,
            "parentId": "root",
            "pathName": "/中国",
            "addCheck": false,
            "isOppo": false,
            "children": [
                {
                    "id": "2",
                    "areaName": "北京",
                    "areaLevel": 2,
                    "parentId": "1",
                    "pathName": "/中国/北京",
                    "addCheck": true,
                    "isOppo": false,
                    "children": [
                        {
                            "id": "3",
                            "areaName": "朝阳区",
                            "areaLevel": 3,
                            "parentId": "2",
                            "pathName": "/中国/北京/朝阳区",
                            "addCheck": false,
                            "isOppo": false,
                            "children": []
                        }
                    ]
                }
            ]
        }
    ]
}
```

## 技术实现要点

1. **数据获取**：通过 `ProjectAreaDomainService.selectAreaListBy()` 获取地区数据
2. **商机过滤**：不调用项目查询逻辑，确保不包含商机信息
3. **树结构构建**：手动构建地区树结构，避免包含商机节点
4. **排序**：按地区名称进行中文排序
5. **异常处理**：完整的异常处理和日志记录

## 注意事项

1. 此接口专门用于获取纯地区结构，不包含任何商机/项目信息
2. 返回的所有节点的 `isOppo` 字段都为 `false`
3. 接口路径为 `/uportal/business-statistics/area-query`，与原接口路径不同
4. 适用于需要地区选择但不需要商机信息的场景
5. 性能更好，因为不需要查询项目数据

## 日志记录

接口会记录以下日志：
- 请求接收日志（INFO级别）
- 成功响应日志（INFO级别）
- 错误日志（ERROR级别）
- 调试日志（DEBUG级别）

## 测试

可以使用提供的单元测试类 `BusinessStatisticsControllerTest` 进行测试验证。
