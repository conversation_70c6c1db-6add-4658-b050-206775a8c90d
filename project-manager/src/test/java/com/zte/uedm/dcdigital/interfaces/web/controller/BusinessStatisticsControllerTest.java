package com.zte.uedm.dcdigital.interfaces.web.controller;

import com.zte.uedm.dcdigital.application.statistics.BusinessStatisticsService;
import com.zte.uedm.dcdigital.common.web.BaseResult;
import com.zte.uedm.dcdigital.domain.aggregate.model.ProjectAreaObj;
import com.zte.uedm.dcdigital.domain.service.ProjectAreaDomainService;
import com.zte.uedm.dcdigital.interfaces.web.vo.AreaTreeVo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * BusinessStatisticsController 测试类
 * 
 * <AUTHOR> Assistant
 */
public class BusinessStatisticsControllerTest {

    @Mock
    private BusinessStatisticsService businessStatisticsService;

    @Mock
    private ProjectAreaDomainService projectAreaDomainService;

    @InjectMocks
    private BusinessStatisticsController businessStatisticsController;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testQueryAreaTree_Success() {
        // 准备测试数据
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("areaName", "北京");

        // 模拟地区数据（不包含商机）
        List<ProjectAreaObj> mockAreaObjs = Arrays.asList(
            createMockAreaObj("1", "北京", 1, "root"),
            createMockAreaObj("2", "朝阳区", 2, "1"),
            createMockAreaObj("3", "海淀区", 2, "1")
        );

        when(projectAreaDomainService.selectAreaListBy(eq(Collections.emptyList()), eq("北京")))
            .thenReturn(mockAreaObjs);

        // 执行测试
        BaseResult<List<AreaTreeVo>> result = businessStatisticsController.queryAreaTree(requestBody);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        
        List<AreaTreeVo> areaTree = result.getData();
        assertEquals(1, areaTree.size()); // 应该只有一个根节点（北京）
        
        AreaTreeVo rootNode = areaTree.get(0);
        assertEquals("北京", rootNode.getAreaName());
        assertEquals(Integer.valueOf(1), rootNode.getAreaLevel());
        assertFalse(rootNode.isOppo()); // 确保不是商机
        
        // 验证子节点
        assertNotNull(rootNode.getChildren());
        assertEquals(2, rootNode.getChildren().size());
        
        for (AreaTreeVo child : rootNode.getChildren()) {
            assertFalse(child.isOppo()); // 确保子节点也不是商机
            assertEquals(Integer.valueOf(2), child.getAreaLevel());
        }
    }

    @Test
    void testQueryAreaTree_EmptyRequestBody() {
        // 测试空请求体
        List<ProjectAreaObj> mockAreaObjs = Arrays.asList(
            createMockAreaObj("1", "北京", 1, "root")
        );

        when(projectAreaDomainService.selectAreaListBy(eq(Collections.emptyList()), eq("")))
            .thenReturn(mockAreaObjs);

        BaseResult<List<AreaTreeVo>> result = businessStatisticsController.queryAreaTree(null);

        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
    }

    @Test
    void testQueryAreaTree_NoAreaData() {
        // 测试没有地区数据的情况
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("areaName", "不存在的地区");

        when(projectAreaDomainService.selectAreaListBy(eq(Collections.emptyList()), eq("不存在的地区")))
            .thenReturn(Collections.emptyList());

        BaseResult<List<AreaTreeVo>> result = businessStatisticsController.queryAreaTree(requestBody);

        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertTrue(result.getData().isEmpty());
    }

    @Test
    void testQueryAreaTree_Exception() {
        // 测试异常情况
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("areaName", "测试");

        when(projectAreaDomainService.selectAreaListBy(any(), any()))
            .thenThrow(new RuntimeException("数据库连接失败"));

        BaseResult<List<AreaTreeVo>> result = businessStatisticsController.queryAreaTree(requestBody);

        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertTrue(result.getMessage().contains("Internal server error"));
    }

    /**
     * 创建模拟的地区对象
     */
    private ProjectAreaObj createMockAreaObj(String id, String areaName, Integer areaLevel, String parentId) {
        ProjectAreaObj areaObj = new ProjectAreaObj();
        areaObj.setId(id);
        areaObj.setAreaName(areaName);
        areaObj.setAreaLevel(areaLevel);
        areaObj.setParentId(parentId);
        areaObj.setPathName("/root/" + areaName);
        return areaObj;
    }

    @Test
    void testAreaTreeStructure_NoBusinessOpportunities() {
        // 验证构建的地区树不包含商机信息
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("areaName", "");

        List<ProjectAreaObj> mockAreaObjs = Arrays.asList(
            createMockAreaObj("1", "中国", 1, "root"),
            createMockAreaObj("2", "北京", 2, "1"),
            createMockAreaObj("3", "上海", 2, "1"),
            createMockAreaObj("4", "朝阳区", 3, "2")
        );

        when(projectAreaDomainService.selectAreaListBy(eq(Collections.emptyList()), eq("")))
            .thenReturn(mockAreaObjs);

        BaseResult<List<AreaTreeVo>> result = businessStatisticsController.queryAreaTree(requestBody);

        assertNotNull(result);
        assertTrue(result.isSuccess());
        
        List<AreaTreeVo> areaTree = result.getData();
        
        // 递归验证所有节点都不是商机
        verifyNoBusinessOpportunities(areaTree);
    }

    /**
     * 递归验证地区树中没有商机信息
     */
    private void verifyNoBusinessOpportunities(List<AreaTreeVo> nodes) {
        if (nodes == null || nodes.isEmpty()) {
            return;
        }
        
        for (AreaTreeVo node : nodes) {
            assertFalse(node.isOppo(), "地区节点不应该是商机: " + node.getAreaName());
            
            // 递归检查子节点
            if (node.getChildren() != null) {
                verifyNoBusinessOpportunities(node.getChildren());
            }
        }
    }
}
